package com.example.offlineitemdelivery.commands;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.models.PendingItem;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class OfflineQueueCommand implements CommandExecutor, TabCompleter {
    
    private final OfflineItemDeliveryPlugin plugin;
    private final ItemDeliveryService itemDeliveryService;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public OfflineQueueCommand(OfflineItemDeliveryPlugin plugin, ItemDeliveryService itemDeliveryService) {
        this.plugin = plugin;
        this.itemDeliveryService = itemDeliveryService;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("offlineitemdelivery.admin")) {
            plugin.sendMessage(sender, "&cYou don't have permission to use this command.");
            return true;
        }
        
        if (args.length == 0) {
            sendUsage(sender, label);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "list":
                return handleListCommand(sender, args);
            case "clear":
                return handleClearCommand(sender, args);
            case "remove":
                return handleRemoveCommand(sender, args);
            case "stats":
                return handleStatsCommand(sender, args);
            default:
                sendUsage(sender, label);
                return true;
        }
    }
    
    private boolean handleListCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            plugin.sendMessage(sender, "&cUsage: /offlinequeue list <player>");
            return true;
        }

        String playerName = args[1];
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(playerName);

        if (targetPlayer == null || (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline())) {
            plugin.sendMessage(sender, "&cPlayer '" + playerName + "' has never played on this server.");
            return true;
        }
        
        try {
            List<PendingItem> pendingItems = itemDeliveryService.getPendingItems(targetPlayer.getUniqueId());
            
            if (pendingItems.isEmpty()) {
                plugin.sendMessage(sender, "&e" + targetPlayer.getName() + " has no pending items.");
                return true;
            }

            plugin.sendMessage(sender, "&a=== Pending Items for " + targetPlayer.getName() + " ===");
            
            for (int i = 0; i < pendingItems.size(); i++) {
                try {
                    PendingItem item = pendingItems.get(i);
                    String status = item.isDelivered() ? ChatColor.GREEN + "[DELIVERED]" : ChatColor.YELLOW + "[PENDING]";
                    String itemName = getItemDisplayName(item);
                    String createdAt = item.getCreatedAt() != null ? dateFormat.format(item.getCreatedAt()) : "Unknown";

                    plugin.sendMessage(sender, "&7" + (i + 1) + ". " + status + "&f " +
                        item.getAmount() + "x " + itemName + "&7 (ID: " + item.getId() +
                        ", Server: " + item.getServerId() + ", Created: " + createdAt + ")");
                } catch (Exception e) {
                    plugin.sendMessage(sender, "&c" + (i + 1) + ". [ERROR] Failed to display item - " + e.getMessage());
                    plugin.getLogger().warning("Error displaying pending item: " + e.getMessage());
                }
            }
            
            plugin.sendMessage(sender, "&aTotal: " + pendingItems.size() + " items");

        } catch (SQLException e) {
            plugin.sendMessage(sender, "&cDatabase error occurred while fetching pending items.");
            plugin.getLogger().severe("Database error in list command: " + e.getMessage());
        }
        
        return true;
    }
    
    private boolean handleClearCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            plugin.sendMessage(sender, "&cUsage: /offlinequeue clear <player>");
            return true;
        }

        String playerName = args[1];
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(playerName);

        if (targetPlayer == null || (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline())) {
            plugin.sendMessage(sender, "&cPlayer '" + playerName + "' has never played on this server.");
            return true;
        }
        
        try {
            int count = itemDeliveryService.getPendingItemCount(targetPlayer.getUniqueId());
            
            if (count == 0) {
                plugin.sendMessage(sender, "&e" + targetPlayer.getName() + " has no pending items to clear.");
                return true;
            }

            boolean success = itemDeliveryService.clearPendingItems(targetPlayer.getUniqueId());

            if (success) {
                plugin.sendMessage(sender, "&aSuccessfully cleared " + count + " pending items for " + targetPlayer.getName() + ".");
                plugin.getLogger().info(sender.getName() + " cleared " + count + " pending items for " + targetPlayer.getName());
            } else {
                plugin.sendMessage(sender, "&cFailed to clear pending items.");
            }

        } catch (SQLException e) {
            plugin.sendMessage(sender, "&cDatabase error occurred while clearing pending items.");
            plugin.getLogger().severe("Database error in clear command: " + e.getMessage());
        }
        
        return true;
    }
    
    private boolean handleRemoveCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            plugin.sendMessage(sender, "&cUsage: /offlinequeue remove <item_id>");
            return true;
        }

        int itemId;
        try {
            itemId = Integer.parseInt(args[1]);
        } catch (NumberFormatException e) {
            plugin.sendMessage(sender, "&cInvalid item ID: " + args[1]);
            return true;
        }

        try {
            boolean success = itemDeliveryService.removePendingItem(itemId);

            if (success) {
                plugin.sendMessage(sender, "&aSuccessfully removed pending item with ID " + itemId + ".");
                plugin.getLogger().info(sender.getName() + " removed pending item with ID " + itemId);
            } else {
                plugin.sendMessage(sender, "&cNo pending item found with ID " + itemId + ".");
            }

        } catch (SQLException e) {
            plugin.sendMessage(sender, "&cDatabase error occurred while removing pending item.");
            plugin.getLogger().severe("Database error in remove command: " + e.getMessage());
        }
        
        return true;
    }
    
    private boolean handleStatsCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            plugin.sendMessage(sender, "&cUsage: /offlinequeue stats <player>");
            return true;
        }

        String playerName = args[1];
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(playerName);

        if (targetPlayer == null || (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline())) {
            plugin.sendMessage(sender, "&cPlayer '" + playerName + "' has never played on this server.");
            return true;
        }

        try {
            int pendingCount = itemDeliveryService.getPendingItemCount(targetPlayer.getUniqueId());
            int maxItems = plugin.getConfig().getInt("settings.max-pending-items", 50);

            plugin.sendMessage(sender, "&a=== Queue Stats for " + targetPlayer.getName() + " ===");
            plugin.sendMessage(sender, "&fPending Items: &e" + pendingCount + "&7/" + maxItems);
            plugin.sendMessage(sender, "&fStatus: " + (targetPlayer.isOnline() ? "&aOnline" : "&cOffline"));

        } catch (SQLException e) {
            plugin.sendMessage(sender, "&cDatabase error occurred while fetching stats.");
            plugin.getLogger().severe("Database error in stats command: " + e.getMessage());
        }
        
        return true;
    }
    
    private void sendUsage(CommandSender sender, String label) {
        plugin.sendMessage(sender, "&a=== OfflineItemDelivery Queue Management ===");
        plugin.sendMessage(sender, "&f/" + label + " list <player>&7 - List pending items for a player");
        plugin.sendMessage(sender, "&f/" + label + " clear <player>&7 - Clear all pending items for a player");
        plugin.sendMessage(sender, "&f/" + label + " remove <item_id>&7 - Remove a specific pending item");
        plugin.sendMessage(sender, "&f/" + label + " stats <player>&7 - Show queue statistics for a player");
    }
    
    private String getItemDisplayName(PendingItem item) {
        if (item == null || item.getItemStack() == null) {
            return "Unknown Item";
        }

        try {
            if (item.getItemStack().hasItemMeta() && item.getItemStack().getItemMeta().hasDisplayName()) {
                return item.getItemStack().getItemMeta().getDisplayName();
            }
            return item.getItemStack().getType().name().toLowerCase().replace('_', ' ');
        } catch (Exception e) {
            // If there's any error getting the item name, return a safe default
            return "Corrupted Item (ID: " + item.getId() + ")";
        }
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            String partial = args[0].toLowerCase();
            for (String subCommand : Arrays.asList("list", "clear", "remove", "stats")) {
                if (subCommand.startsWith(partial)) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2 && !args[0].equalsIgnoreCase("remove")) {
            String partial = args[1].toLowerCase();

            // First, add online players (no disk I/O required)
            for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                if (onlinePlayer.getName().toLowerCase().startsWith(partial)) {
                    completions.add(onlinePlayer.getName());
                }
            }

            // Limit offline player suggestions to prevent server lockup
            int minChars = plugin.getConfig().getInt("settings.min-chars-for-offline-suggestions", 3);
            if (completions.size() < 10 && partial.length() >= minChars) {
                addLimitedOfflinePlayerSuggestions(completions, partial);
            }
        }
        
        return completions;
    }

    /**
     * Adds a limited number of offline player suggestions to avoid performance issues.
     * This method is designed to prevent server lockups when there are many offline players.
     */
    private void addLimitedOfflinePlayerSuggestions(List<String> completions, String partial) {
        // Get a configurable limit for offline player suggestions
        int maxOfflineSuggestions = plugin.getConfig().getInt("settings.max-offline-tab-suggestions", 20);
        int addedCount = 0;

        // Use a more efficient approach - get offline players but limit iteration
        OfflinePlayer[] offlinePlayers = Bukkit.getOfflinePlayers();

        // Sort by last played time (most recent first) to show relevant players
        // But limit the sorting to avoid performance issues
        int maxToSort = Math.min(offlinePlayers.length, 1000); // Only sort the first 1000

        for (int i = 0; i < maxToSort && addedCount < maxOfflineSuggestions; i++) {
            OfflinePlayer player = offlinePlayers[i];

            // Skip if player is online (already added) or has no name
            if (player.isOnline() || player.getName() == null) {
                continue;
            }

            // Check if name matches and isn't already in completions
            String playerName = player.getName();
            if (playerName.toLowerCase().startsWith(partial) && !completions.contains(playerName)) {
                completions.add(playerName);
                addedCount++;
            }
        }
    }
}
